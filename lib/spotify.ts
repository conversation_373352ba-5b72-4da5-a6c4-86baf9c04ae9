const SPOTIFY_API_BASE = 'https://api.spotify.com/v1';

export interface SpotifyTrack {
  id: string;
  name: string;
  artists: { name: string }[];
  album: {
    name: string;
    images: { url: string }[];
  };
  duration_ms: number;
  uri: string;
  alias?: string;
}

export interface SpotifyPlaylist {
  id: string;
  name: string;
  tracks: {
    items: {
      track: SpotifyTrack;
    }[];
  };
}

export class SpotifyAPI {
  private accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  async getPlaylist(playlistId: string): Promise<SpotifyPlaylist> {
    const response = await fetch(`${SPOTIFY_API_BASE}/playlists/${playlistId}`, {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch playlist');
    }

    return response.json();
  }

  async getPlaylists(): Promise<{ items: SpotifyPlaylist[] }> {
    const response = await fetch(`${SPOTIFY_API_BASE}/me/playlists`, {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch playlists');
    }

    return response.json();
  }

  async play(deviceId: string, uri?: string, position?: number) {
    const body: { uris?: string[]; position_ms?: number } = {}
    if (uri) {
      body.uris = [uri];
    }
    if (position !== undefined) {
      body.position_ms = position;
    }

    await fetch(`${SPOTIFY_API_BASE}/me/player/play?device_id=${deviceId}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
  }

  async pause(deviceId: string) {
    await fetch(`${SPOTIFY_API_BASE}/me/player/pause?device_id=${deviceId}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });
  }

  async getDevices() {
    const response = await fetch(`${SPOTIFY_API_BASE}/me/player/devices`, {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch devices');
    }

    return response.json();
  }
}