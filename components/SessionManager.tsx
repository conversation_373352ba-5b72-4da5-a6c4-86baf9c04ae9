'use client';

import { useState, useEffect, useCallback } from 'react';
import { SpotifyTrack } from '@/lib/spotify';
import { PersistenceService } from '@/lib/persistence';
import { usePlayback } from '@/contexts/PlaybackContext';
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { ColumnView } from './ColumnView';

interface SessionManagerProps {
  availableTracks: SpotifyTrack[];
  playlistId?: string;
  onSessionDataUpdate?: (sessionData: { columns: Column[], lastTrackId?: string }) => void;
  loadSessionTrigger?: any;
}

interface Column {
  id: string;
  title: string;
  trackIds: string[];
}

export function SessionManager({
  availableTracks,
  playlistId = '',
  onSessionDataUpdate,
  loadSessionTrigger,
}: SessionManagerProps) {
  const playback = usePlayback();
  const [columns, setColumns] = useState<Column[]>([
    { id: 'ambient', title: 'Ambient', trackIds: [] },
    { id: 'combat', title: 'Combat', trackIds: [] },
  ]);
  const [aliases, setAliases] = useState<Record<string, string>>({});
  const [activeTrack, setActiveTrack] = useState<SpotifyTrack | null>(null);
  const [lastSelectedTrackId, setLastSelectedTrackId] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load saved state on mount and when playlist changes
  useEffect(() => {
    if (!playlistId) return;

    const savedAliases = localStorage.getItem('trackAliases');
    if (savedAliases) {
      setAliases(JSON.parse(savedAliases));
    }

    // Try to load autosaved session for this playlist
    const autoSession = PersistenceService.loadAutoSession();
    if (autoSession && autoSession.playlistId === playlistId) {
      setColumns(autoSession.columns);
      if (autoSession.lastTrackId) {
        setLastSelectedTrackId(autoSession.lastTrackId);
        // Find and highlight the last selected track (but don't play it)
        const track = availableTracks.find(t => t.id === autoSession.lastTrackId);
        if (track) {
          // Just store it, don't auto-play
          console.log('Last selected track:', track.name);
        }
      }
    }
  }, [playlistId, availableTracks]);

  // Auto-save when columns change
  useEffect(() => {
    if (!playlistId) return;

    const timer = setTimeout(() => {
      PersistenceService.saveAutoSession({
        name: 'Autosave',
        playlistId,
        columns,
        lastTrackId: lastSelectedTrackId || undefined,
      });
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timer);
  }, [columns, playlistId, lastSelectedTrackId]);

  // Update parent with current session data
  useEffect(() => {
    if (onSessionDataUpdate) {
      onSessionDataUpdate({
        columns,
        lastTrackId: lastSelectedTrackId || undefined,
      });
    }
  }, [columns, lastSelectedTrackId, onSessionDataUpdate]);

  // Handle load session trigger
  useEffect(() => {
    if (loadSessionTrigger) {
      setColumns(loadSessionTrigger.columns);
      if (loadSessionTrigger.lastTrackId) {
        setLastSelectedTrackId(loadSessionTrigger.lastTrackId);
      }
    }
  }, [loadSessionTrigger]);

  const handleAliasChange = (trackId: string, alias: string) => {
    const newAliases = { ...aliases, [trackId]: alias };
    setAliases(newAliases);
    localStorage.setItem('trackAliases', JSON.stringify(newAliases));
  };

  const handleTrackPlay = (track: SpotifyTrack) => {
    setLastSelectedTrackId(track.id);
    playback.playTrack(track);
  };


  const addColumn = () => {
    const name = prompt('Enter column name:');
    if (name && name.trim()) {
      const newColumn: Column = {
        id: `column-${Date.now()}`,
        title: name.trim(),
        trackIds: [],
      };
      setColumns([...columns, newColumn]);
    }
  };

  const removeColumn = (columnId: string) => {
    setColumns(columns.filter((col) => col.id !== columnId));
  };

  const handleDragStart = (event: DragStartEvent) => {
    const activeId = event.active.id as string;
    const track = availableTracks.find(t => t.id === activeId);
    setActiveTrack(track || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find which container the active item is from
    const activeContainer = findContainer(activeId);

    // Determine the target container
    let overContainer: string | null;
    if (columns.some(col => col.id === overId) || overId === 'source') {
      // Dropped on a column itself
      overContainer = overId;
    } else {
      // Dropped on an item, find its container
      overContainer = findContainer(overId);
    }

    if (!overContainer) return;

    // Handle the drop
    if (activeContainer === 'source' && overContainer !== 'source') {
      // Moving from source to a column
      const targetColumn = columns.find(col => col.id === overContainer);
      if (targetColumn && !targetColumn.trackIds.includes(activeId)) {
        setColumns(cols => cols.map(col => {
          if (col.id === overContainer) {
            return { ...col, trackIds: [...col.trackIds, activeId] };
          }
          return col;
        }));
      }
    } else if (activeContainer !== 'source' && overContainer === 'source') {
      // Moving from column back to source
      setColumns(cols => cols.map(col => {
        if (col.id === activeContainer) {
          return { ...col, trackIds: col.trackIds.filter(id => id !== activeId) };
        }
        return col;
      }));
    } else if (activeContainer !== 'source' && overContainer !== 'source' && activeContainer !== overContainer) {
      // Moving between columns
      setColumns(cols => cols.map(col => {
        if (col.id === activeContainer) {
          return { ...col, trackIds: col.trackIds.filter(id => id !== activeId) };
        }
        if (col.id === overContainer) {
          if (!col.trackIds.includes(activeId)) {
            return { ...col, trackIds: [...col.trackIds, activeId] };
          }
        }
        return col;
      }));
    } else if (activeContainer === overContainer && activeContainer !== 'source') {
      // Reordering within the same column
      const column = columns.find(col => col.id === activeContainer);
      if (column) {
        const oldIndex = column.trackIds.indexOf(activeId);
        const newIndex = column.trackIds.indexOf(overId);
        if (oldIndex !== -1 && newIndex !== -1) {
          setColumns(cols => cols.map(col => {
            if (col.id === activeContainer) {
              const newTrackIds = arrayMove(col.trackIds, oldIndex, newIndex);
              return { ...col, trackIds: newTrackIds };
            }
            return col;
          }));
        }
      }
    }
    setActiveTrack(null);
  };

  const findContainer = (id: string): string | null => {
    // Check if it's in source
    if (availableTracks.some(t => t.id === id)) {
      // Check if it's also in a column
      const column = columns.find(col => col.trackIds.includes(id));
      return column ? column.id : 'source';
    }

    // Check columns
    const column = columns.find(col => col.trackIds.includes(id));
    return column?.id || null;
  };

  const getTracksForColumn = (trackIds: string[]): SpotifyTrack[] => {
    return trackIds
      .map(id => availableTracks.find(t => t.id === id))
      .filter((t): t is SpotifyTrack => t !== undefined);
  };

  // Get tracks that are not in any column for the source
  const getSourceTracks = (): SpotifyTrack[] => {
    const usedTrackIds = new Set(columns.flatMap(col => col.trackIds));
    return availableTracks.filter(t => !usedTrackIds.has(t.id));
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="flex gap-4 h-full p-6 overflow-x-auto">
        {/* Source column */}
        <div className="w-80 flex-shrink-0">
          <ColumnView
            id="source"
            title="Uncategorized"
            tracks={getSourceTracks()}
            aliases={aliases}
            onTrackPlay={handleTrackPlay}
            onAliasChange={handleAliasChange}
          />
        </div>

        {/* Scenario columns */}
        {columns.map((column) => (
          <ColumnView
            key={column.id}
            id={column.id}
            title={column.title}
            tracks={getTracksForColumn(column.trackIds)}
            aliases={aliases}
            onTrackPlay={handleTrackPlay}
            onAliasChange={handleAliasChange}
            onRemove={() => removeColumn(column.id)}
            canRemove={!['ambient', 'combat'].includes(column.id)}
          />
        ))}

        {/* Add column button */}
        <button
          onClick={addColumn}
          className="w-16 flex-shrink-0 bg-gray-100 hover:bg-gray-200 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center transition-colors"
          aria-label="Add new column"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="12" y1="5" x2="12" y2="19" />
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button>
      </div>
      <DragOverlay>
        {activeTrack ? (
          <div className="bg-white rounded-lg shadow-2xl border border-gray-200 p-2 opacity-90 transform rotate-3">
            <div className="flex items-center gap-2">
              {activeTrack.album.images[0] && (
                <img
                  src={activeTrack.album.images[0].url}
                  alt={activeTrack.album.name}
                  className="w-10 h-10 rounded"
                />
              )}
              <div className="min-w-0">
                <div className="font-medium text-sm truncate text-gray-900">
                  {activeTrack.name}
                </div>
                <div className="text-xs truncate text-gray-500">
                  {activeTrack.artists.map((a) => a.name).join(', ')}
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}