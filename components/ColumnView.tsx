'use client';

import { SpotifyTrack } from '@/lib/spotify';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { usePlayback } from '@/contexts/PlaybackContext';
import { DraggableTrack } from './DraggableTrack';

interface ColumnViewProps {
  id: string;
  title: string;
  tracks: SpotifyTrack[];
  aliases: Record<string, string>;
  onTrackPlay: (track: SpotifyTrack) => void;
  onAliasChange: (trackId: string, alias: string) => void;
  onRemove?: () => void;
  canRemove?: boolean;
}

export function ColumnView({
  id,
  title,
  tracks,
  aliases,
  onTrackPlay,
  onAliasChange,
  onRemove,
  canRemove = false,
}: ColumnViewProps) {
  const { setNodeRef, isOver } = useDroppable({ id });
  const playback = usePlayback();

  return (
    <div
      className={`flex-1 min-w-[320px] max-w-[400px] bg-gray-100 rounded-lg flex flex-col h-full transition-all ${
        isOver ? 'ring-2 ring-blue-400 bg-blue-50' : ''
      }`}
    >
      <div className="flex items-center justify-between p-3 bg-white rounded-t-lg border-b border-gray-200">
        <h3 className="font-semibold text-base text-gray-900">{title}</h3>
        {canRemove && (
          <button
            onClick={onRemove}
            className="text-gray-400 hover:text-red-500 transition-colors p-1"
            aria-label={`Remove ${title} column`}
          >
            <svg width="18" height="18" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      <div
        ref={setNodeRef}
        className={`flex-1 overflow-y-auto p-3 transition-colors ${
          isOver ? 'bg-blue-50' : ''
        }`}
      >
        <SortableContext
          items={tracks.map((t) => t.id)}
          strategy={verticalListSortingStrategy}
        >
          {tracks.length === 0 ? (
            <div className={`text-center py-12 px-4 border-2 border-dashed rounded-lg transition-colors ${
              isOver ? 'border-blue-400 bg-white' : 'border-gray-300'
            }`}>
              <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v16m8-8H4" />
              </svg>
              <p className="text-sm text-gray-500">Drop tracks here</p>
            </div>
          ) : (
            <div className="space-y-2">
              {tracks.map((track) => (
                <DraggableTrack
                  key={track.id}
                  track={track}
                  alias={aliases[track.id] || ''}
                  isPlaying={playback.currentTrack?.uri === track.uri && playback.isPlaying}
                  isSelected={playback.currentTrack?.uri === track.uri}
                  onPlay={() => onTrackPlay(track)}
                />
              ))}
            </div>
          )}
        </SortableContext>
      </div>
    </div>
  );
}