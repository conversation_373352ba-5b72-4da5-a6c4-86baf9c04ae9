'use client';

import { useState, useEffect } from 'react';
import { usePlayback } from '@/contexts/PlaybackContext';
import { SpotifyTrack } from '@/lib/spotify';

interface TrackControlsProps {
  track: SpotifyTrack;
  alias: string;
}

export function TrackControls({ track, alias }: TrackControlsProps) {
    const playback = usePlayback()
    const [showAnnotateModal, setShowAnnotateModal] = useState(false)
    const [bookmarkName, setBookmarkName] = useState('')
    const [showBookmarks, setShowBookmarks] = useState(false)
    const [bookmarks, setBookmarks] = useState<
        Array<{
            id: string
            name: string
            timestamp: number
            createdAt: string
        }>
    >([])

    const handleStopClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        playback.stopTrack()
    }

    const handleAnnotateClick = (e: React.MouseEvent) => {
        e.stopPropagation()
        setShowAnnotateModal(true)
    }

    const handleSaveBookmark = () => {
        if (!bookmarkName.trim()) return

        const bookmark = {
            id: Date.now().toString(),
            name: bookmarkName.trim(),
            timestamp: playback.position,
            createdAt: new Date().toISOString()
        }

        const bookmarksKey = `bookmarks_${track.id}`
        const existingBookmarks = JSON.parse(
            localStorage.getItem(bookmarksKey) || '[]'
        )

        const updatedBookmarks = [...existingBookmarks, bookmark]
        localStorage.setItem(bookmarksKey, JSON.stringify(updatedBookmarks))

        loadBookmarks()
        setShowAnnotateModal(false)
        setBookmarkName('')
    }

    const formatTime = (ms: number) => {
        const seconds = Math.floor(ms / 1000)
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const loadBookmarks = () => {
        const bookmarksKey = `bookmarks_${track.id}`
        const existingBookmarks = JSON.parse(
            localStorage.getItem(bookmarksKey) || '[]'
        )
        setBookmarks(existingBookmarks)
    }

    const handleBookmarkClick = (timestamp: number) => {
        playback.seekTo(timestamp)
    }

    const handleDeleteBookmark = (bookmarkId: string) => {
        const bookmarksKey = `bookmarks_${track.id}`
        const updatedBookmarks = bookmarks.filter((b) => b.id !== bookmarkId)
        localStorage.setItem(bookmarksKey, JSON.stringify(updatedBookmarks))
        setBookmarks(updatedBookmarks)
    }

    const toggleBookmarksList = () => {
        if (!showBookmarks) {
            loadBookmarks()
        }
        setShowBookmarks(!showBookmarks)
    }

    useEffect(() => {
        loadBookmarks()
    }, [track.id])

    return (
        <div className="flex justify-center gap-4 mt-6">
            <button
                onClick={handleStopClick}
                disabled={playback.currentTrack?.uri !== track.uri}
                className="p-1 rounded transition-colors bg-red-500 hover:bg-red-600 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
                <svg
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <rect x="6" y="6" width="12" height="12" />
                </svg>
            </button>

            <button
                onClick={handleAnnotateClick}
                disabled={playback.currentTrack?.uri !== track.uri}
                className="p-1 rounded transition-colors bg-purple-500 hover:bg-purple-600 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                title="Add bookmark"
            >
                <svg
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z" />
                </svg>
            </button>

            <button
                onClick={toggleBookmarksList}
                className={`p-1 rounded transition-colors ${
                    bookmarks.length > 0
                        ? 'bg-indigo-500 hover:bg-indigo-600 text-white'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                disabled={bookmarks.length === 0}
                title={`${bookmarks.length} bookmarks`}
            >
                <svg
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" />
                </svg>
            </button>

            {showAnnotateModal && (
                <div
                    className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
                    onClick={() => setShowAnnotateModal(false)}
                >
                    <div
                        className="bg-white rounded-lg p-6 w-96 text-gray-900"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <h3 className="text-lg font-semibold mb-4 text-gray-900">
                            Add Bookmark
                        </h3>

                        <div className="mb-4">
                            <p className="text-sm text-gray-700 mb-2">
                                Track:{' '}
                                <span className="font-medium">
                                    {alias || track.name}
                                </span>
                            </p>
                            <p className="text-sm text-gray-700 mb-4">
                                Timestamp:{' '}
                                <span className="font-medium">
                                    {formatTime(playback.position)}
                                </span>
                            </p>
                        </div>

                        <input
                            type="text"
                            value={bookmarkName}
                            onChange={(e) => setBookmarkName(e.target.value)}
                            placeholder="Enter bookmark name..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 mb-4 text-gray-900 placeholder-gray-500"
                            autoFocus
                            onKeyDown={(e) =>
                                e.key === 'Enter' && handleSaveBookmark()
                            }
                        />

                        <div className="flex justify-end gap-2">
                            <button
                                onClick={() => {
                                    setShowAnnotateModal(false)
                                    setBookmarkName('')
                                }}
                                className="px-4 py-2 text-gray-800 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleSaveBookmark}
                                disabled={!bookmarkName.trim()}
                                className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                Save Bookmark
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {showBookmarks && (
                <div
                    className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
                    onClick={() => setShowBookmarks(false)}
                >
                    <div
                        className="bg-white rounded-lg p-6 w-[500px] max-h-[70vh] overflow-y-auto text-gray-900"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <h3 className="text-lg font-semibold mb-4 text-gray-900">
                            Bookmarks for {alias || track.name}
                        </h3>

                        {bookmarks.length === 0 ? (
                            <p className="text-gray-700 text-center py-4">
                                No bookmarks for this track
                            </p>
                        ) : (
                            <div className="space-y-2">
                                {bookmarks.map((bookmark) => (
                                    <div
                                        key={bookmark.id}
                                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                                    >
                                        <div className="flex-1">
                                            <div className="font-medium">
                                                {bookmark.name}
                                            </div>
                                            <div className="text-sm text-gray-700">
                                                {formatTime(bookmark.timestamp)}{' '}
                                                •{' '}
                                                {new Date(
                                                    bookmark.createdAt
                                                ).toLocaleString()}
                                            </div>
                                        </div>
                                        <div className="flex gap-2">
                                            <button
                                                onClick={() =>
                                                    handleBookmarkClick(
                                                        bookmark.timestamp
                                                    )
                                                }
                                                className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm transition-colors"
                                            >
                                                Play
                                            </button>
                                            <button
                                                onClick={() =>
                                                    handleDeleteBookmark(
                                                        bookmark.id
                                                    )
                                                }
                                                className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm transition-colors"
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        <div className="flex justify-end mt-4">
                            <button
                                onClick={() => setShowBookmarks(false)}
                                className="px-4 py-2 text-gray-800 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}