'use client';

import { useState } from 'react';
import { PersistenceService, SavedSession } from '@/lib/persistence';

interface SessionControlsProps {
  playlistId: string;
  onLoadSession: (session: SavedSession) => void;
  onSaveSession: (name: string) => void;
}

export function SessionControls({ playlistId, onLoadSession, onSaveSession }: SessionControlsProps) {
  const [showMenu, setShowMenu] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [sessionName, setSessionName] = useState('');
  const [sessions, setSessions] = useState<SavedSession[]>([]);

  const openMenu = () => {
    const savedSessions = PersistenceService.getSavedSessions().filter(s => s.playlistId === playlistId);
    setSessions(savedSessions);
    setShowMenu(true);
  };

  const handleSave = () => {
    if (sessionName.trim()) {
      onSaveSession(sessionName.trim());
      setSessionName('');
      setShowSaveDialog(false);
    }
  };

  const handleLoad = (session: SavedSession) => {
    onLoadSession(session);
    setShowMenu(false);
  };

  const handleDelete = (sessionId: string) => {
    if (confirm('Are you sure you want to delete this session?')) {
      PersistenceService.deleteSession(sessionId);
      const updatedSessions = sessions.filter(s => s.id !== sessionId);
      setSessions(updatedSessions);
    }
  };

  return (
      <>
          <div className="fixed bottom-6 right-6 flex flex-col gap-2 items-end">
              <div className="flex gap-2">
                  <button
                      onClick={() => setShowSaveDialog(true)}
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-lg transition-all hover:shadow-xl flex items-center gap-2"
                  >
                      <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                      >
                          <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                      </svg>
                      Save
                  </button>
                  <button
                      onClick={openMenu}
                      className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-lg transition-all hover:shadow-xl flex items-center gap-2"
                  >
                      <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                      >
                          <path
                              fillRule="evenodd"
                              d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                              clipRule="evenodd"
                          />
                      </svg>
                      Load
                  </button>
              </div>

              <div className="text-xs text-gray-500 bg-white px-2 py-1 rounded shadow">
                  Auto-saving enabled
              </div>
          </div>

          {/* Save Dialog */}
          {showSaveDialog && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 w-96 text-gray-900">
                      <h3 className="text-lg font-semibold mb-4 text-gray-900">
                          Save Session
                      </h3>
                      <input
                          type="text"
                          value={sessionName}
                          onChange={(e) => setSessionName(e.target.value)}
                          placeholder="Enter session name..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 text-gray-900 placeholder-gray-500"
                          autoFocus
                          onKeyDown={(e) => e.key === 'Enter' && handleSave()}
                      />
                      <div className="flex justify-end gap-2 mt-4">
                          <button
                              onClick={() => {
                                  setShowSaveDialog(false)
                                  setSessionName('')
                              }}
                              className="px-4 py-2 text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                          >
                              Cancel
                          </button>
                          <button
                              onClick={handleSave}
                              disabled={!sessionName.trim()}
                              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                              Save
                          </button>
                      </div>
                  </div>
              </div>
          )}

          {/* Load Menu */}
          {showMenu && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 w-[500px] max-h-[70vh] overflow-y-auto text-gray-900">
                      <h3 className="text-lg font-semibold mb-4 text-gray-900">
                          Load Session
                      </h3>

                      {sessions.length === 0 ? (
                          <p className="text-gray-500 text-center py-4">
                              No saved sessions for this playlist
                          </p>
                      ) : (
                          <div className="space-y-2">
                              {sessions.map((session) => (
                                  <div
                                      key={session.id}
                                      className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                                  >
                                      <div className="flex-1">
                                          <div className="font-medium text-gray-900">
                                              {session.name}
                                          </div>
                                          <div className="text-sm text-gray-500">
                                              {new Date(
                                                  session.updatedAt
                                              ).toLocaleString()}
                                          </div>
                                          <div className="text-xs text-gray-400 mt-1">
                                              {session.columns
                                                  .map(
                                                      (c) =>
                                                          `${c.title} (${c.trackIds.length})`
                                                  )
                                                  .join(', ')}
                                          </div>
                                      </div>
                                      <div className="flex gap-2">
                                          <button
                                              onClick={() =>
                                                  handleLoad(session)
                                              }
                                              className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm"
                                          >
                                              Load
                                          </button>
                                          <button
                                              onClick={() =>
                                                  handleDelete(session.id)
                                              }
                                              className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm"
                                          >
                                              Delete
                                          </button>
                                      </div>
                                  </div>
                              ))}
                          </div>
                      )}

                      <div className="flex justify-end mt-4">
                          <button
                              onClick={() => setShowMenu(false)}
                              className="px-4 py-2 text-gray-600 hover:text-gray-800"
                          >
                              Close
                          </button>
                      </div>
                  </div>
              </div>
          )}
      </>
  )
}