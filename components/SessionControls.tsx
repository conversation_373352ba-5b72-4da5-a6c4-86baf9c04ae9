'use client';

import { useState } from 'react';
import { PersistenceService, SavedSession } from '@/lib/persistence';
import { SaveSessionModal } from './SaveSessionModal'
import { LoadSessionModal } from './LoadSessionModal'

interface SessionControlsProps {
    playlistId: string
    onLoadSession: (session: SavedSession) => void
    onSaveSession: (name: string) => void
}

export function SessionControls({
    playlistId,
    onLoadSession,
    onSaveSession
}: SessionControlsProps) {
    const [showLoadModal, setShowLoadModal] = useState(false)
    const [showSaveModal, setShowSaveModal] = useState(false)
    const [sessions, setSessions] = useState<SavedSession[]>([])

    const openLoadModal = () => {
        const savedSessions = PersistenceService.getSavedSessions().filter(
            (s) => s.playlistId === playlistId
        )
        setSessions(savedSessions)
        setShowLoadModal(true)
    }

    const handleSave = (sessionName: string) => {
        onSaveSession(sessionName)
    }

    const handleLoad = (session: SavedSession) => {
        onLoadSession(session)
    }

    const handleDelete = (sessionId: string) => {
        PersistenceService.deleteSession(sessionId)
        // Refresh the sessions list
        const savedSessions = PersistenceService.getSavedSessions().filter(
            (s) => s.playlistId === playlistId
        )
        setSessions(savedSessions)
    }

    return (
        <>
            <div className="fixed bottom-6 right-6 flex flex-col gap-2 items-end">
                <div className="flex gap-2">
                    <button
                        onClick={() => setShowSaveModal(true)}
                        className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-lg transition-all hover:shadow-xl flex items-center gap-2"
                    >
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                        </svg>
                        Save
                    </button>
                    <button
                        onClick={openLoadModal}
                        className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-lg transition-all hover:shadow-xl flex items-center gap-2"
                    >
                        <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fillRule="evenodd"
                                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                                clipRule="evenodd"
                            />
                        </svg>
                        Load
                    </button>
                </div>

                <div className="text-xs text-gray-500 bg-white px-2 py-1 rounded shadow">
                    Auto-saving enabled
                </div>
            </div>

            <SaveSessionModal
                isOpen={showSaveModal}
                onClose={() => setShowSaveModal(false)}
                onSave={handleSave}
            />

            <LoadSessionModal
                isOpen={showLoadModal}
                onClose={() => setShowLoadModal(false)}
                sessions={sessions}
                onLoad={handleLoad}
                onDelete={handleDelete}
            />
        </>
    )
}